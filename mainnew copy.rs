use anyhow::Result;
use log::info;
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::time;
use tokio::sync::mpsc;
use std::collections::HashSet;

/// 统计事件类型
#[derive(Debug, Clone)]
pub enum StatsEvent {
    Search,
    Opportunity,
    Success(String), // bundle_id
}

/// 高性能异步统计管理器
pub struct StatsManager {
    /// 总搜索次数
    total_searches: AtomicU64,
    /// 发现的套利机会数量
    opportunities_found: AtomicU64,
    /// 成功执行套利交易的次数
    successful_executions: AtomicU64,
    /// 启动时间
    start_time: Instant,
    /// 事件发送器
    event_sender: mpsc::UnboundedSender<StatsEvent>,
}

impl StatsManager {
    /// 创建新的统计管理器实例
    pub fn new() -> Self {
        let (event_sender, event_receiver) = mpsc::unbounded_channel();
        
        let stats = Self {
            total_searches: AtomicU64::new(0),
            opportunities_found: AtomicU64::new(0),
            successful_executions: AtomicU64::new(0),
            start_time: Instant::now(),
            event_sender,
        };
        
        // 启动异步事件处理器
        Self::start_event_processor(
            event_receiver,
            stats.opportunities_found.clone(),
            stats.successful_executions.clone(),
        );
        
        stats
    }
    
    /// 启动异步事件处理器
    fn start_event_processor(
        mut event_receiver: mpsc::UnboundedReceiver<StatsEvent>,
        opportunities_counter: Arc<AtomicU64>,
        success_counter: Arc<AtomicU64>,
    ) {
        tokio::spawn(async move {
            let mut processed_bundle_ids = HashSet::new();
            
            while let Some(event) = event_receiver.recv().await {
                match event {
                    StatsEvent::Search => {
                        // Search 已经在主线程中直接处理，这里不需要处理
                    }
                    StatsEvent::Opportunity => {
                        opportunities_counter.fetch_add(1, Ordering::Relaxed);
                    }
                    StatsEvent::Success(bundle_id) => {
                        // 检查是否是新的bundle ID
                        if processed_bundle_ids.insert(bundle_id) {
                            success_counter.fetch_add(1, Ordering::Relaxed);
                        }
                        
                        // 限制集合大小，避免内存泄漏
                        if processed_bundle_ids.len() > 10000 {
                            processed_bundle_ids.clear();
                        }
                    }
                }
            }
        });
    }
    
    /// 启动定期统计打印 (每秒一次)
    pub fn start_periodic_stats_printer(self: Arc<Self>) {
        tokio::spawn(async move {
            let mut interval = time::interval(Duration::from_secs(1));
            
            loop {
                interval.tick().await;
                self.print_stats();
            }
        });
    }
    
    /// 记录一次搜索 (超快速，直接原子操作)
    pub fn record_search(&self) {
        self.total_searches.fetch_add(1, Ordering::Relaxed);
    }
    
    /// 记录发现套利机会 (异步，不阻塞主流程)
    pub fn record_opportunity(&self) {
        // 发送到异步处理器，立即返回
        let _ = self.event_sender.send(StatsEvent::Opportunity);
    }
    
    /// 记录成功执行 (异步，不阻塞主流程)
    pub fn record_success(&self, bundle_id: String) {
        // 发送到异步处理器，立即返回
        let _ = self.event_sender.send(StatsEvent::Success(bundle_id));
    }
    
    /// 从日志消息中解析并记录成功
    pub fn record_success_from_log(&self, log_message: &str) {
        if let Some(bundle_id) = Self::extract_bundle_id(log_message) {
            self.record_success(bundle_id);
        }
    }
    
    /// 提取bundle ID的纯函数
    fn extract_bundle_id(log_message: &str) -> Option<String> {
        // 查找 "bundle id:" 模式
        if let Some(start) = log_message.find("bundle id:") {
            let bundle_part = &log_message[start + 10..];
            
            // 提取bundle ID
            let bundle_id = bundle_part
                .trim()
                .split_whitespace()
                .next()
                .unwrap_or("")
                .trim_matches('"');
                
            // 验证是否为有效的bundle ID (64字符十六进制)
            if bundle_id.len() == 64 && bundle_id.chars().all(|c| c.is_ascii_hexdigit()) {
                return Some(bundle_id.to_string());
            }
        }
        None
    }
    
    /// 打印当前统计信息
    pub fn print_stats(&self) {
        let searches = self.total_searches.load(Ordering::Relaxed);
        let opportunities = self.opportunities_found.load(Ordering::Relaxed);
        let successes = self.successful_executions.load(Ordering::Relaxed);
        
        info!(
            "[STATS] Total searches: {}  |  Opportunities: {}  |  Success: {}",
            searches, opportunities, successes
        );
    }
}

/// 全局统计管理器
static GLOBAL_STATS: std::sync::OnceLock<Arc<StatsManager>> = std::sync::OnceLock::new();

/// 初始化全局统计管理器
pub fn init_global_stats() -> Arc<StatsManager> {
    let stats = Arc::new(StatsManager::new());
    GLOBAL_STATS.set(stats.clone()).expect("统计管理器只能初始化一次");
    
    // 启动定期打印
    stats.clone().start_periodic_stats_printer();
    
    stats
}

/// 获取全局统计管理器
pub fn get_global_stats() -> &'static Arc<StatsManager> {
    GLOBAL_STATS.get().expect("统计管理器未初始化")
}

/// 便捷函数：记录搜索 (超快速)
#[inline]
pub fn record_search() {
    get_global_stats().record_search();
}

/// 便捷函数：记录机会 (异步)
#[inline]
pub fn record_opportunity() {
    get_global_stats().record_opportunity();
}

/// 便捷函数：记录成功 (异步)
#[inline]
pub fn record_success_from_log(log_message: &str) {
    get_global_stats().record_success_from_log(log_message);
}