use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::Path;

/// 套利代币对配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ArbPairConfig {
    /// 输入代币地址
    pub input_mint: String,
    /// 输出代币地址
    pub output_mint: String,
    /// 可能的金额列表，将随机选择其中一个
    pub amounts: Vec<u64>,
    /// 是否只使用直达路由
    pub only_direct_routes: bool,
    /// 最大账户数
    pub max_accounts: u64,
    /// 套利对名称(可选)，用于日志记录
    pub name: Option<String>,
    /// 套利轮询间隔(毫秒)，如果不设置则使用全局间隔
    pub poll_interval_ms: Option<u64>,
}

/// 全局机器人配置
#[derive(Debug, Serialize, Deserialize)]
pub struct BotConfig {
    /// 全局轮询间隔(毫秒)，如果套利对没有指定则使用此值
    pub poll_interval_ms: u64,
    /// Jito小费百分比列表，将随机选择其中一个
    pub jito_tip_percentages: Vec<f64>,
    /// 套利代币对列表
    pub arb_pairs: Vec<ArbPairConfig>,
    /// RPC URL(可选)，如果设置则覆盖环境变量
    pub rpc_url: Option<String>,
    /// Jupiter API URL(可选)，如果设置则覆盖环境变量
    pub jupiter_api_url: Option<String>,
    /// Jito RPC URL(可选)，如果设置则覆盖环境变量
    pub jito_rpc_url: Option<String>,
    /// Jito RPC URLs列表(可选)，如果设置则覆盖单个jito_rpc_url
    pub jito_rpc_urls: Option<Vec<String>>,
    /// 价格更新间隔(毫秒)，默认为30000
    pub price_update_interval_ms: Option<u64>,
}

/// 配置管理器
pub struct ConfigManager {
    config: BotConfig,
}

impl ConfigManager {
    /// 从配置文件创建新的配置管理器
    pub fn new(config_path: &Path) -> Result<Self> {
        let config_str = fs::read_to_string(config_path)
            .context(format!("无法读取配置文件: {:?}", config_path))?;
            
        let config: BotConfig = serde_yaml::from_str(&config_str)
            .context("无法解析YAML配置")?;
            
        // 验证配置有效性
        Self::validate_config(&config)?;
        
        Ok(Self { config })
    }
    
    /// 验证配置是否有效
    fn validate_config(config: &BotConfig) -> Result<()> {
        // 验证全局轮询间隔
        if config.poll_interval_ms == 0 {
            return Err(anyhow::anyhow!("poll_interval_ms必须大于0"));
        }
        
        // 验证Jito小费百分比
        if config.jito_tip_percentages.is_empty() {
            return Err(anyhow::anyhow!("jito_tip_percentages不能为空"));
        }
        
        for percentage in &config.jito_tip_percentages {
            if *percentage <= 0.0 || *percentage >= 1.0 {
                return Err(anyhow::anyhow!("jito_tip_percentages必须在0和1之间"));
            }
        }
        
        // 验证套利对
        if config.arb_pairs.is_empty() {
            return Err(anyhow::anyhow!("没有定义套利代币对"));
        }
        
        // 验证每个套利对配置
        for (idx, pair) in config.arb_pairs.iter().enumerate() {
            if pair.amounts.is_empty() {
                let pair_name = pair.name.clone().unwrap_or_else(|| format!("套利对{}", idx + 1));
                return Err(anyhow::anyhow!("套利对 {} 的amounts不能为空", pair_name));
            }
            
            // 验证轮询间隔(如果有设置)
            if let Some(interval) = pair.poll_interval_ms {
                if interval == 0 {
                    let pair_name = pair.name.clone().unwrap_or_else(|| format!("套利对{}", idx + 1));
                    return Err(anyhow::anyhow!("套利对 {} 的poll_interval_ms必须大于0", pair_name));
                }
            }
        }
        
        Ok(())
    }
    
    /// 获取全局配置
    pub fn get_config(&self) -> &BotConfig {
        &self.config
    }
    
    /// 获取有效的套利对列表
    pub fn get_valid_arb_pairs(&self) -> Vec<ArbPairConfig> {
        self.config.arb_pairs.clone()
    }
    
    /// 获取RPC URL，优先使用配置文件中的设置
    pub fn get_rpc_url(&self) -> String {
        self.config.rpc_url.clone().unwrap_or_else(|| {
            std::env::var("RPC_URL").unwrap_or_else(|_| "https://solana-rpc.publicnode.com".to_string())
        })
    }
    
    /// 获取Jupiter API URL，优先使用配置文件中的设置
    pub fn get_jupiter_api_url(&self) -> String {
        self.config.jupiter_api_url.clone().unwrap_or_else(|| {
            std::env::var("JUP_V6_API_BASE_URL").unwrap_or_else(|_| "http://104.164.110.97:8080".to_string())
        })
    }
    
    /// 获取Jito RPC URLs列表
    pub fn get_jito_rpc_urls(&self) -> Vec<String> {
        // 优先使用jito_rpc_urls列表
        if let Some(urls) = &self.config.jito_rpc_urls {
            if !urls.is_empty() {
                return urls.clone();
            }
        }
        
        // 如果没有设置多端点列表，则使用单端点并包装为列表返回
        vec![self.get_jito_rpc_url()]
    }
    
    /// 获取Jito RPC URL，优先使用配置文件中的设置
    pub fn get_jito_rpc_url(&self) -> String {
        self.config.jito_rpc_url.clone().unwrap_or_else(|| {
            std::env::var("JITO_RPC_URL").unwrap_or_else(|_| "https://frankfurt.mainnet.block-engine.jito.wtf/api/v1/bundles".to_string())
        })
    }
    
    /// 获取价格更新间隔，默认为30秒
    pub fn get_price_update_interval(&self) -> u64 {
        self.config.price_update_interval_ms.unwrap_or(30000)
    }
} 