配置系统设计
1.创建一个专门的配置系统，使用serde_yaml从YAML文件加载配置：

// 套利代币对配置
struct ArbPairConfig {
    input_mint: String,
    output_mint: String,
    amounts: Vec<u64>,
    only_direct_routes: bool,
    max_accounts: u64,
    name: Option<String>,  // 可选的代币对名称，便于日志记录
}

// 全局配置
struct BotConfig {
    poll_interval_ms: u64,  // 高频轮询间隔
    jito_tip_percentages: Vec<f64>,  // 动态小费百分比列表
    arb_pairs: Vec<ArbPairConfig>,  // 套利代币对列表
}


_____________________________________________________________________

2. 高频监控与并发执行
使用Tokio的异步任务系统实现多代币对的并发监控：


// 为每个套利代币对创建一个异步任务
for pair_config in config.arb_pairs {
    let bot_clone = bot.clone();
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(Duration::from_millis(config.poll_interval_ms));
        loop {
            interval.tick().await;
            if let Err(e) = bot_clone.check_arbitrage_opportunity(&pair_config).await {
                log::error!("套利检查错误 {}: {}", pair_config.name.unwrap_or_default(), e);
            }
        }
    });
}

_____________________________________________________________________

3.动态小费计算
从配置的百分比列表中随机选择一个值：


fn calculate_dynamic_jito_tip(&self, difflamports: u64, percentages: &[f64]) -> u64 {
    let mut rng = thread_rng();
    let percentage = *percentages.choose(&mut rng).unwrap_or(&0.5);
    let jito_tip = (difflamports as f64 * percentage) as u64;
    jito_tip.min(MAX_TIP_LAMPORTS)
}


_____________________________________________________________________

修改主循环以支持多代币对并发
async fn run_multi_pair_bot() -> Result<()> {
    // 加载配置
    let config_manager = ConfigManager::new(Path::new("config.yaml"))?;
    let config = config_manager.get_config();
    
    // 创建机器人实例
    let bot = Arc::new(ArbitrageBot::new()?);
    
    // 初始化各种缓存和服务
    bot.initialize_ip_rotator().await?;
    bot.initialize_blockhash_cache().await?;
    bot.initialize_price_cache().await?;
    bot.preload_alt_cache().await?;
    
    // 获取有效的套利对
    let valid_pairs = config_manager.get_valid_arb_pairs();
    log::info!("已加载 {} 个有效套利代币对", valid_pairs.len());
    
    // 为每个套利对创建异步任务
    let mut handles = Vec::new();
    for pair in valid_pairs {
        let bot_clone = bot.clone();
        let pair_name = pair.name.clone().unwrap_or_else(|| format!("{}->{}", pair.input_mint, pair.output_mint));
        let poll_interval = Duration::from_millis(config.poll_interval_ms);
        
        let handle = tokio::spawn(async move {
            log::info!("启动套利监控: {}", pair_name);
            let mut interval = tokio::time::interval(poll_interval);
            
            loop {
                interval.tick().await;
                match bot_clone.check_arbitrage_opportunity(&pair).await {
                    Ok(_) => {},
                    Err(e) => log::error!("套利检查错误 {}: {}", pair_name, e),
                }
            }
        });
        
        handles.push(handle);
    }
    
    // 等待所有任务完成（实际上它们会一直运行）
    for handle in handles {
        handle.await?;
    }
    
    Ok(())
}
______________________________________________________________________________________________


重点来了   重点来了  重点来了

感谢您的反馈和问题。我将对套利机器人设计进行调整，并回答您提出的具体问题。
关于您的问题的回答
关于name字段: 是的，name可以随便写，它只是为了在日志中更好地标识套利对。您可以按照自己喜欢的方式命名。
关于更高度自定义的配置: 完全可以实现每个套利对有自己独立的轮询间隔。我们可以在ArbPairConfig中添加poll_interval_ms字段，让每个套利对有自己的轮询频率。
关于Quote1参数: 是的，第二次报价的only_direct_routes、slippage_bps和max_accounts可以直接沿用第一次报价的设置。
关于随机选择金额: 是的，pair_config.amounts.choose(&mut rng)是从您在配置文件中指定的金额列表中随机选择一个。
关于注释和代码风格: 我会保持与原始bot.rs类似的注释风格，并尽可能保留原有逻辑结构。
关于二进制分发: 是的，Rust编译后会生成target目录中的二进制文件，这些文件可以直接分发给用户。用户需要的配置可以通过config.yaml提供，不需要修改代码或重新编译。
完善的实现方案
下面是更加详细和符合您需求的实现方案：

———————————————————
1. 配置模块 (new_config.rs)

use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::Path;

/// 套利代币对配置
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ArbPairConfig {
    /// 输入代币地址
    pub input_mint: String,
    /// 输出代币地址
    pub output_mint: String,
    /// 可能的金额列表，将随机选择其中一个
    pub amounts: Vec<u64>,
    /// 是否只使用直达路由
    pub only_direct_routes: bool,
    /// 最大账户数
    pub max_accounts: u64,
    /// 套利对名称(可选)，用于日志记录
    pub name: Option<String>,
    /// 套利轮询间隔(毫秒)，如果不设置则使用全局间隔
    pub poll_interval_ms: Option<u64>,
}

/// 全局机器人配置
#[derive(Debug, Serialize, Deserialize)]
pub struct BotConfig {
    /// 全局轮询间隔(毫秒)，如果套利对没有指定则使用此值
    pub poll_interval_ms: u64,
    /// Jito小费百分比列表，将随机选择其中一个
    pub jito_tip_percentages: Vec<f64>,
    /// 套利代币对列表
    pub arb_pairs: Vec<ArbPairConfig>,
    /// RPC URL(可选)，如果设置则覆盖环境变量
    pub rpc_url: Option<String>,
    /// Jupiter API URL(可选)，如果设置则覆盖环境变量
    pub jupiter_api_url: Option<String>,
    /// Jito RPC URL(可选)，如果设置则覆盖环境变量
    pub jito_rpc_url: Option<String>,
    /// 价格更新间隔(毫秒)，默认为30000
    pub price_update_interval_ms: Option<u64>,
}

/// 配置管理器
pub struct ConfigManager {
    config: BotConfig,
}

impl ConfigManager {
    /// 从配置文件创建新的配置管理器
    pub fn new(config_path: &Path) -> Result<Self> {
        let config_str = fs::read_to_string(config_path)
            .context(format!("无法读取配置文件: {:?}", config_path))?;
            
        let config: BotConfig = serde_yaml::from_str(&config_str)
            .context("无法解析YAML配置")?;
            
        // 验证配置有效性
        Self::validate_config(&config)?;
        
        Ok(Self { config })
    }
    
    /// 验证配置是否有效
    fn validate_config(config: &BotConfig) -> Result<()> {
        // 验证全局轮询间隔
        if config.poll_interval_ms == 0 {
            return Err(anyhow::anyhow!("poll_interval_ms必须大于0"));
        }
        
        // 验证Jito小费百分比
        if config.jito_tip_percentages.is_empty() {
            return Err(anyhow::anyhow!("jito_tip_percentages不能为空"));
        }
        
        for percentage in &config.jito_tip_percentages {
            if *percentage <= 0.0 || *percentage >= 1.0 {
                return Err(anyhow::anyhow!("jito_tip_percentages必须在0和1之间"));
            }
        }
        
        // 验证套利对
        if config.arb_pairs.is_empty() {
            return Err(anyhow::anyhow!("没有定义套利代币对"));
        }
        
        // 验证每个套利对配置
        for (idx, pair) in config.arb_pairs.iter().enumerate() {
            if pair.amounts.is_empty() {
                let pair_name = pair.name.clone().unwrap_or_else(|| format!("套利对{}", idx + 1));
                return Err(anyhow::anyhow!("套利对 {} 的amounts不能为空", pair_name));
            }
            
            // 验证轮询间隔(如果有设置)
            if let Some(interval) = pair.poll_interval_ms {
                if interval == 0 {
                    let pair_name = pair.name.clone().unwrap_or_else(|| format!("套利对{}", idx + 1));
                    return Err(anyhow::anyhow!("套利对 {} 的poll_interval_ms必须大于0", pair_name));
                }
            }
        }
        
        Ok(())
    }
    
    /// 获取全局配置
    pub fn get_config(&self) -> &BotConfig {
        &self.config
    }
    
    /// 获取有效的套利对列表
    pub fn get_valid_arb_pairs(&self) -> Vec<ArbPairConfig> {
        // 在这里可以添加更多的验证逻辑
        self.config.arb_pairs.clone()
    }
    
    /// 获取RPC URL，优先使用配置文件中的设置
    pub fn get_rpc_url(&self) -> String {
        self.config.rpc_url.clone().unwrap_or_else(|| {
            std::env::var("RPC_URL").unwrap_or_else(|_| "https://solana-rpc.publicnode.com".to_string())
        })
    }
    
    /// 获取Jupiter API URL，优先使用配置文件中的设置
    pub fn get_jupiter_api_url(&self) -> String {
        self.config.jupiter_api_url.clone().unwrap_or_else(|| {
            std::env::var("JUP_V6_API_BASE_URL").unwrap_or_else(|_| "http://**************:8080".to_string())
        })
    }
    
    /// 获取Jito RPC URL，优先使用配置文件中的设置
    pub fn get_jito_rpc_url(&self) -> String {
        self.config.jito_rpc_url.clone().unwrap_or_else(|| {
            std::env::var("JITO_RPC_URL").unwrap_or_else(|_| "https://frankfurt.mainnet.block-engine.jito.wtf/api/v1/bundles".to_string())
        })
    }
    
    /// 获取价格更新间隔，默认为30秒
    pub fn get_price_update_interval(&self) -> u64 {
        self.config.price_update_interval_ms.unwrap_or(30000)
    }
}



———————————————————————————————

2. 扩展套利机器人 (new_bot.rs)

use crate::new_config::{ArbPairConfig, BotConfig, ConfigManager};
use crate::consts;
use crate::types::*;
use crate::jito_tip_manager::JitoTipManager;
use crate::alt_cache_manager::AltCacheManager;
use crate::blockhash_cache_manager::BlockhashCacheManager;
use crate::price_cache_manager::PriceCacheManager;
use crate::IpRotator::IpRotator;
use anyhow::{Context, Result};
use base64::Engine;
use log;
use rand::seq::SliceRandom;
use rand::thread_rng;
use solana_client::rpc_client::RpcClient;
use solana_sdk::{
    commitment_config::CommitmentConfig,
    compute_budget::ComputeBudgetInstruction,
    instruction::{AccountMeta, Instruction},
    pubkey::Pubkey,
    signature::{read_keypair_file, Keypair},
    signer::Signer,
    system_instruction,
    transaction::VersionedTransaction,
};
use std::{env, str::FromStr, time::Instant, fs, path::Path, sync::Arc};

pub struct MultiPairArbitrageBot {
    client: RpcClient,
    http_client: reqwest::Client,
    payer: Keypair,
    jito_tip_manager: JitoTipManager,
    alt_cache_manager: AltCacheManager,
    blockhash_cache_manager: BlockhashCacheManager,
    price_cache_manager: PriceCacheManager,
    ip_rotator: IpRotator,
    config_manager: ConfigManager,
}

impl MultiPairArbitrageBot {
    /// 创建新的多代币对套利机器人
    pub fn new(config_path: &Path) -> Result<Self> {
        // 加载配置文件
        let config_manager = ConfigManager::new(config_path)?;
        
        // 获取密钥路径
        let keypair_path = env::var("KEYPAIR_PATH").expect("必须设置KEYPAIR_PATH环境变量");
        let payer = read_keypair_file(&keypair_path).expect("无法读取密钥文件");

        log::info!("付款账户地址: {:?}", bs58::encode(payer.pubkey()).into_string());
        
        // 确保数据目录存在
        let data_dir = Path::new(consts::DATA_DIR);
        if !data_dir.exists() {
            fs::create_dir_all(data_dir)?;
        }
        
        // 获取RPC URL
        let rpc_url = config_manager.get_rpc_url();
        
        // 创建RPC客户端
        let client = RpcClient::new_with_commitment(
            rpc_url.clone(),
            CommitmentConfig::processed(),
        );
        
        // 创建RPC客户端的Arc包装，用于缓存管理器
        let client_arc = Arc::new(RpcClient::new_with_commitment(
            rpc_url,
            CommitmentConfig::processed(),
        ));
        
        // 初始化 JitoTipManager
        let jito_tip_manager = JitoTipManager::new(consts::TIP_ACCOUNTS_COUNT, data_dir)?;
        
        // 初始化 AltCacheManager
        let alt_cache_manager = AltCacheManager::new(client_arc.clone(), data_dir)?;
        
        // 初始化 BlockhashCacheManager (1秒更新一次)
        let blockhash_cache_manager = BlockhashCacheManager::new(client_arc, 1000);

        // 初始化 PriceCacheManager
        let price_update_interval = config_manager.get_price_update_interval(); 
        let price_cache_manager = PriceCacheManager::new(price_update_interval);

        // 初始化 IP轮询器
        let ip_rotator = IpRotator::new();

        Ok(Self {
            client,
            http_client: reqwest::Client::new(),
            payer,
            jito_tip_manager,
            alt_cache_manager,
            blockhash_cache_manager,
            price_cache_manager,
            ip_rotator,
            config_manager,
        })
    }

    /// USDC利润转换为SOL lamports - 使用价格缓存管理器
    pub fn convert_usdc_profit_to_sol_lamports(&self, usdc_profit: u64) -> u64 {
        // 使用价格缓存管理器的转换方法
        self.price_cache_manager.convert_usdc_profit_to_sol_lamports(usdc_profit)
    }

    /// 检查单个套利机会
    pub async fn check_arbitrage_opportunity(&self, pair_config: &ArbPairConfig) -> Result<()> {
        let start = Instant::now();
        let pair_name = pair_config.name.clone().unwrap_or_else(|| 
            format!("{}->{}", pair_config.input_mint, pair_config.output_mint));

        // 从金额列表中随机选择一个金额
        let mut rng = thread_rng();
        let amount = match pair_config.amounts.choose(&mut rng) {
            Some(&amount) => amount,
            None => {
                return Err(anyhow::anyhow!("套利对 {} 的amounts列表为空", pair_name));
            }
        };

        log::debug!("套利对: {}, 使用金额: {}", pair_name, amount);
        
        // Quote 0: INPUT -> OUTPUT
        let quote0_params = QuoteParams {
            input_mint: pair_config.input_mint.clone(),
            output_mint: pair_config.output_mint.clone(),
            amount: amount.to_string(),
            only_direct_routes: pair_config.only_direct_routes,
            slippage_bps: 0,
            max_accounts: pair_config.max_accounts,
        };
        
        // 获取第一个报价
        let quote0_resp = self.get_quote(&quote0_params).await?;

        // Quote 1: OUTPUT -> INPUT
        let quote1_params = QuoteParams {
            input_mint: pair_config.output_mint.clone(),
            output_mint: quote0_resp.input_mint.clone(),
            amount: quote0_resp.out_amount.clone(),
            only_direct_routes: pair_config.only_direct_routes,
            slippage_bps: 0,
            max_accounts: pair_config.max_accounts,
        };
        
        // 获取第二个报价
        let quote1_resp = self.get_quote(&quote1_params).await?;

        // 计算潜在利润
        let quote1_out_amount = quote1_resp.out_amount.parse::<u64>()?;
        let quote0_in_amount = amount;
        
        if quote1_out_amount <= quote0_in_amount {
            log::debug!(
                "套利对 {} 不盈利，跳过。差额: -{}",
                pair_name,
                quote0_in_amount - quote1_out_amount
            );
            return Ok(());
        }
        
        // 计算利润
        let profit = quote1_out_amount - quote0_in_amount;
        
        // 根据硬编码公钥判断代币类型
        let is_wsol = quote0_resp.input_mint == "So11111111111111111111111111111111111111112";
        
        // 统一计算difflamports
        let difflamports = if is_wsol {
            // WSOL路径：直接使用profit作为lamports
            profit
        } else {
            // USDC路径：转换为SOL lamports (使用价格缓存管理器)
            self.convert_usdc_profit_to_sol_lamports(profit)
        };
        
        // 打印日志 - 根据代币类型显示不同信息
        if is_wsol {
            log::info!("{}: WSOL profit: {} lamports", pair_name, profit);
        } else {
            log::info!("{}: USDC profit: {}, 转换为: {} lamports", pair_name, profit, difflamports);
        }
        
        // 获取Jito小费百分比列表
        let jito_tip_percentages = &self.config_manager.get_config().jito_tip_percentages;
        
        // 动态计算Jito小费
        let jito_tip = self.calculate_dynamic_jito_tip(difflamports, jito_tip_percentages);
        log::info!("{}: Jito小费 (lamports): {}", pair_name, jito_tip);
        
        // 统一使用lamports阈值
        const THRESHOLD_LAMPORTS: u64 = 500;  // 例如 0.000005 SOL
        
        // 计算真实利润 = difflamports - jito_tip - BASE_TX_FEE
        const BASE_TX_FEE: u64 = 10000;  // Solana 基础交易费用 (lamports)
        let real_profit = difflamports.saturating_sub(jito_tip).saturating_sub(BASE_TX_FEE);
        
        // 打印真实利润信息
        log::info!("{}: 真实利润 (lamports): {}", pair_name, real_profit);
        
        // 使用真实利润与阈值比较
        if real_profit > THRESHOLD_LAMPORTS {
            // 执行套利交易
            log::info!("{}: 执行套利交易...", pair_name);
            self.execute_arbitrage(quote0_resp, quote1_resp, jito_tip).await?;
            
            let duration = start.elapsed();
            log::info!("{}: 套利完成，总耗时: {}ms", pair_name, duration.as_millis());
        }

        Ok(())
    }

    /// 动态计算Jito小费
    fn calculate_dynamic_jito_tip(&self, difflamports: u64, percentages: &[f64]) -> u64 {
        // 从百分比列表中随机选择一个值
        let mut rng = thread_rng();
        let percentage = *percentages.choose(&mut rng).unwrap_or(&0.5);
        
        // 计算小费金额
        let jito_tip = (difflamports as f64 * percentage) as u64;
        
        // 限制最大小费金额
        jito_tip.min(consts::MAX_TIP_LAMPORTS)
    }

    /// 执行套利交易
    async fn execute_arbitrage(
        &self,
        quote0: QuoteResponse,
        quote1: QuoteResponse,
        jito_tip: u64,
    ) -> Result<()> {
        // 获取一个小号代付账户
        let tip_account = self.jito_tip_manager.get_next_tip_account();
        log::info!("使用小费账户: {}", tip_account.pubkey().to_string());
        
        // 合并两个报价，创建一个完整的交易路径
        let mut merged_quote = quote0.clone();
        merged_quote.output_mint = quote1.output_mint;
        merged_quote.out_amount = quote1.out_amount;
        merged_quote.other_amount_threshold =
            (quote0.other_amount_threshold.parse::<u64>()? + jito_tip).to_string();        
        merged_quote.price_impact_pct = 0.0.to_string();
        merged_quote.route_plan = [quote0.route_plan, quote1.route_plan].concat();

        // 准备 Jupiter API 的交换数据
        let swap_data = SwapData {
            user_public_key: bs58::encode(self.payer.pubkey()).into_string(),
            wrap_and_unwrap_sol: false,
            use_shared_accounts: false,
            compute_unit_price_micro_lamports: 1,
            dynamic_compute_unit_limit: true,
            skip_user_accounts_rpc_calls: true,
            quote_response: merged_quote,
        };

        // 从 Jupiter 获取交换指令
        let instructions_resp: SwapInstructionResponse =
            self.get_swap_instructions(&swap_data).await?;

        // 构建主账户交易指令列表
        let mut main_tx_instructions = Vec::new();

        // 1. 添加计算预算指令
        let compute_budget_ix =
            ComputeBudgetInstruction::set_compute_unit_limit(instructions_resp.compute_unit_limit);
        main_tx_instructions.push(compute_budget_ix);

        // 2. 添加设置指令
        for setup_ix in instructions_resp.setup_instructions {
            main_tx_instructions.push(self.convert_instruction_data(setup_ix)?);
        }

        // 3. 添加交换指令
        main_tx_instructions.push(self.convert_instruction_data(instructions_resp.swap_instruction)?);

        // 4. 从主账户向小号转账的指令（包含 tip + 返回金额 + 网络费用）
        let transfer_to_tip_account_ix = system_instruction::transfer(
            &self.payer.pubkey(),
            &tip_account.pubkey(),
            jito_tip + consts::RETURN_AMOUNT + consts::NETWORK_FEE,
        );
        main_tx_instructions.push(transfer_to_tip_account_ix);

        // 获取缓存的区块哈希
        log::info!("获取缓存的区块哈希...");
        let start_blockhash_time = Instant::now();
        let blockhash = self.blockhash_cache_manager.get_blockhash();
        let blockhash_age = self.blockhash_cache_manager.get_time_since_last_update();
        let end_blockhash_time = start_blockhash_time.elapsed();
        
        log::info!(
            "获取区块哈希耗时: {}ms, 区块哈希: {}, 距上次更新: {}ms",
            end_blockhash_time.as_millis(),
            blockhash,
            blockhash_age
        );

        // 转换地址查找表
        let address_lookup_tables = self
            .get_address_lookup_tables(&instructions_resp.address_lookup_table_addresses)
            .await?;

        // 创建主账户交易消息
        let main_message = solana_sdk::message::v0::Message::try_compile(
            &self.payer.pubkey(),
            &main_tx_instructions,
            &address_lookup_tables,
            blockhash,
        )?;

        // 创建并签名主账户交易
        let main_transaction = VersionedTransaction::try_new(
            solana_sdk::message::VersionedMessage::V0(main_message),
            &[&self.payer],
        )?;

        // 构建小号代付交易指令列表
        let mut tip_tx_instructions = Vec::new();

        // 1. 向 Jito 支付小费
        let random_jito_tip_account = consts::get_random_jito_tip_account();
        log::info!("使用Jito小费地址: {}", random_jito_tip_account);
        let pay_jito_tip_ix = system_instruction::transfer(
            &tip_account.pubkey(),
            &Pubkey::from_str(random_jito_tip_account)?,
            jito_tip,
        );
        tip_tx_instructions.push(pay_jito_tip_ix);

        // 2. 将固定金额返回给主账户
        let return_to_main_ix = system_instruction::transfer(
            &tip_account.pubkey(),
            &self.payer.pubkey(),
            consts::RETURN_AMOUNT,
        );
        tip_tx_instructions.push(return_to_main_ix);

        // 创建小号代付交易消息（不使用地址查找表）
        let tip_message = solana_sdk::message::v0::Message::try_compile(
            &tip_account.pubkey(),
            &tip_tx_instructions,
            &[], // 小号交易不需要地址查找表
            blockhash,
        )?;

        // 创建并签名小号代付交易
        let tip_transaction = VersionedTransaction::try_new(
            solana_sdk::message::VersionedMessage::V0(tip_message),
            &[&tip_account],
        )?;

        log::info!("主交易: {:?}", main_transaction.signatures[0]);
        log::info!("小号交易: {:?}", tip_transaction.signatures[0]);

        // 将两个交易作为 bundle 发送到 Jito
        self.send_bundle_to_jito(vec![main_transaction, tip_transaction]).await?;

        Ok(())
    }

    async fn get_quote(&self, params: &QuoteParams) -> Result<QuoteResponse> {
        let jupiter_api_url = self.config_manager.get_jupiter_api_url();
        let response: QuoteResponse = self
            .http_client
            .get(format!("{}/quote", jupiter_api_url))
            .query(&params)
            .send()
            .await?
            .json()
            .await?;
        Ok(response)
    }

    async fn get_swap_instructions(&self, params: &SwapData) -> Result<SwapInstructionResponse> {
        let jupiter_api_url = self.config_manager.get_jupiter_api_url();
        let response: SwapInstructionResponse = self
            .http_client
            .post(format!("{}/swap-instructions", jupiter_api_url))
            .json(&params)
            .send()
            .await?
            .json()
            .await?;
        Ok(response)
    }

    async fn send_bundle_to_jito(&self, transactions: Vec<VersionedTransaction>) -> Result<()> {
        // Serialize transactions for Jito bundle
        let serialized_txs: Vec<Vec<u8>> = transactions
            .iter()
            .map(|tx| bincode::serialize(tx).map_err(anyhow::Error::from))
            .collect::<Result<_>>()?;
        let base58_txs = serialized_txs
            .iter()
            .map(|tx| bs58::encode(tx).into_string())
            .collect::<Vec<_>>();

        // Prepare bundle request
        let bundle_request = serde_json::json!({
            "jsonrpc": "2.0",
            "id": 1,
            "method": "sendBundle",
            "params": [base58_txs]
        });

        // 使用IP轮询器发送bundle
        let jito_rpc_url = self.config_manager.get_jito_rpc_url();
        let bundle_result = self.ip_rotator.send_jito_bundle(bundle_request, &jito_rpc_url).await?;
        
        let bundle_id = bundle_result["result"].as_str().unwrap_or("unknown");

        log::info!("发送到Jito，bundle id: {}", bundle_id);

        Ok(())
    }

    fn convert_instruction_data(&self, ix_data: InstructionData) -> Result<Instruction> {
        let program_id = Pubkey::from_str(&ix_data.program_id)?;

        let accounts: Vec<AccountMeta> = ix_data
            .accounts
            .into_iter()
            .map(|acc| {
                let pubkey = Pubkey::from_str(&acc.pubkey).expect("Failed to parse pubkey");
                AccountMeta {
                    pubkey,
                    is_signer: acc.is_signer,
                    is_writable: acc.is_writable,
                }
            })
            .collect();

        // 使用 base64 0.21.0 版本的新 API
        let data = base64::engine::general_purpose::STANDARD.decode(&ix_data.data)?;

        Ok(Instruction {
            program_id,
            accounts,
            data,
        })
    }

    async fn get_address_lookup_tables(
        &self,
        addresses: &[String],
    ) -> Result<Vec<solana_sdk::address_lookup_table_account::AddressLookupTableAccount>> {
        // 使用 ALT 缓存管理器获取地址查找表
        self.alt_cache_manager.get_address_lookup_table_accounts(addresses).await
    }

    // 添加预热 ALT 缓存的方法
    pub async fn preload_alt_cache(&self) -> Result<()> {
        let alt_list_file = Path::new("src/LookupTableAccount.rs");
        self.alt_cache_manager.initialize_from_address_list(alt_list_file).await?;
        log::info!("ALT 缓存预热完成");
        Ok(())
    }
    
    // 初始化区块哈希缓存
    pub async fn initialize_blockhash_cache(&self) -> Result<()> {
        self.blockhash_cache_manager.initialize().await?;
        log::info!("区块哈希缓存初始化完成");
        Ok(())
    }

    // 初始化价格缓存
    pub async fn initialize_price_cache(&self) -> Result<()> {
        self.price_cache_manager.initialize().await?;
        log::info!("价格缓存初始化完成");
        Ok(())
    }

    // 初始化IP轮询器
    pub async fn initialize_ip_rotator(&self) -> Result<()> {
        self.ip_rotator.initialize().await?;
        log::info!("IP轮询器初始化完成");
        Ok(())
    }
    
    // 获取配置管理器
    pub fn get_config_manager(&self) -> &ConfigManager {
        &self.config_manager
    }
}

_____________________________________________________________________

3. 主程序模块 (new_main.rs)


use anyhow::Result;
use new_bot::MultiPairArbitrageBot;
use env_logger::Builder;
use log::{LevelFilter, info, error};
use std::io::Write;
use std::path::Path;
use std::sync::Arc;
use tokio::task::JoinHandle;

mod bot;
mod new_bot;
mod consts;
mod types;
mod new_config;
mod jito_tip_manager;
mod alt_cache_manager;
mod blockhash_cache_manager;
mod price_cache_manager;
mod IpRotator;

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志记录器，添加时间戳
    Builder::new()
        .format(|buf, record| {
            writeln!(
                buf,
                "{} [{}] {}",
                chrono::Local::now().format("%Y-%m-%d %H:%M:%S"),
                record.level(),
                record.args()
            )
        })
        .filter(None, LevelFilter::Info)
        .init();

    // 加载环境变量
    dotenv::dotenv().ok();

    // 配置文件路径
    let config_path = Path::new("config.yaml");
    
    // 创建并初始化机器人
    info!("正在创建多代币对套利机器人...");
    let bot = Arc::new(MultiPairArbitrageBot::new(config_path)?);
    
    // 初始化IP轮询器
    info!("开始初始化IP轮询器...");
    if let Err(e) = bot.initialize_ip_rotator().await {
        error!("初始化IP轮询器失败: {}", e);
    }
    
    // 初始化区块哈希缓存
    info!("开始初始化区块哈希缓存...");
    if let Err(e) = bot.initialize_blockhash_cache().await {
        error!("初始化区块哈希缓存失败: {}", e);
    }
    
    // 初始化价格缓存管理器
    info!("开始初始化价格缓存管理器...");
    if let Err(e) = bot.initialize_price_cache().await {
        error!("初始化价格缓存管理器失败: {}", e);
    }
    
    // 预热 ALT 缓存
    info!("开始预热 ALT 缓存...");
    if let Err(e) = bot.preload_alt_cache().await {
        error!("预热 ALT 缓存失败: {}", e);
    }

    // 获取有效的套利代币对
    let config = bot.get_config_manager().get_config();
    let arb_pairs = bot.get_config_manager().get_valid_arb_pairs();
    
    info!("已加载 {} 个有效套利代币对", arb_pairs.len());
    
    // 为每个套利对创建异步任务
    let mut task_handles: Vec<JoinHandle<()>> = Vec::new();
    
    for pair in arb_pairs {
        let bot_clone = bot.clone();
        let pair_name = pair.name.clone().unwrap_or_else(|| 
            format!("{}->{}", pair.input_mint, pair.output_mint));
        
        // 获取该套利对的轮询间隔，如果没有设置则使用全局间隔
        let poll_interval_ms = pair.poll_interval_ms.unwrap_or(config.poll_interval_ms);
        
        info!("启动套利对 [{}] 监控，轮询间隔: {}ms", pair_name, poll_interval_ms);
        
        let handle = tokio::spawn(async move {
            let mut interval = tokio::time::interval(tokio::time::Duration::from_millis(poll_interval_ms));
            
            loop {
                interval.tick().await;
                if let Err(e) = bot_clone.check_arbitrage_opportunity(&pair).await {
                    error!("套利对 [{}] 检查错误: {}", pair_name, e);
                }
            }
        });
        
        task_handles.push(handle);
    }
    
    info!("所有套利代币对监控任务已启动，等待执行...");

    // 等待所有任务完成（实际上它们会一直运行）
    for handle in task_handles {
        if let Err(e) = handle.await {
            error!("套利任务异常终止: {}", e);
        }
    }

    Ok(())
}



________________________________________

# 套利机器人全局配置
poll_interval_ms: 10  # 默认轮询间隔(毫秒)
jito_tip_percentages:  # Jito小费百分比列表(随机选择)
  - 0.4
  - 0.5
  - 0.65
  - 0.78

# 自定义RPC URL(可选)
# rpc_url: "https://solana-rpc.publicnode.com"
# jupiter_api_url: "http://**************:8080"
# jito_rpc_url: "https://frankfurt.mainnet.block-engine.jito.wtf/api/v1/bundles"

# 价格更新间隔(可选，默认30秒)
# price_update_interval_ms: 30000

# 套利代币对列表
arb_pairs:
  # USDC -> 代币1 -> USDC (活跃度高，轮询间隔短)
  - input_mint: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
    output_mint: "9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump"
    amounts: [1000000, 2000000, 4000000, 5000000]
    only_direct_routes: true
    max_accounts: 20
    name: "USDC-Token1"
    poll_interval_ms: 3  # 每3毫秒检查一次(高频)
  
  # USDC -> 代币2 -> USDC (中等活跃度)
  - input_mint: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
    output_mint: "7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr"
    amounts: [********, ********, ********, ********]
    only_direct_routes: true
    max_accounts: 20
    name: "USDC-Token2"
    poll_interval_ms: 10  # 每10毫秒检查一次(中频)
  
  # WSOL -> 代币3 -> WSOL (活跃度较低)
  - input_mint: "So11111111111111111111111111111111111111112"
    output_mint: "7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr"
    amounts: [********, ********, ********, ********]
    only_direct_routes: false
    max_accounts: 40
    name: "WSOL-Token3"
    poll_interval_ms: 15  # 每15毫秒检查一次(低频)