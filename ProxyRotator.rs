// src/ProxyRotator.rs
use anyhow::Result;
use reqwest::{Client, ClientBuilder, Proxy};
use serde_json::Value;
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use log::{info, warn};

pub struct ProxyRotator {
    available_proxies: Arc<Mutex<Vec<ProxyConfig>>>,
    current_index: Arc<Mutex<usize>>,
    // 为每个代理缓存HTTP客户端
    client_cache: Arc<Mutex<HashMap<String, Client>>>,
    default_client: Client,
    jito_endpoints: Arc<Mutex<Vec<String>>>,
}

#[derive(Debug, Clone)]
pub struct ProxyConfig {
    pub proxy_url: String,     // 完整代理URL
    pub username: String,
    pub password: String,
    pub host: String,
    pub port: u16,
}

impl ProxyRotator {
    pub fn new() -> Self {
        let default_client = ClientBuilder::new()
            .timeout(Duration::from_secs(5))
            .pool_idle_timeout(Duration::from_secs(90))
            .tcp_keepalive(Duration::from_secs(60))
            .pool_max_idle_per_host(10)
            .build()
            .expect("Failed to build default HTTP client");

        Self {
            available_proxies: Arc::new(Mutex::new(Vec::new())),
            current_index: Arc::new(Mutex::new(0)),
            client_cache: Arc::new(Mutex::new(HashMap::new())),
            default_client,
            jito_endpoints: Arc::new(Mutex::new(Vec::new())),
        }
    }

    /// 从daili.txt文件加载代理列表
    pub async fn load_proxies_from_file(&self, file_path: &str) -> Result<()> {
        info!("从文件 {} 加载代理列表...", file_path);
        
        let content = std::fs::read_to_string(file_path)?;
        let mut proxies = Vec::new();
        
        for line in content.lines() {
            let line = line.trim();
            if line.is_empty() || line.starts_with('#') {
                continue;
            }
            
            // 解析代理URL: ******************************:port
            if let Ok(proxy_config) = self.parse_proxy_url(line) {
                proxies.push(proxy_config);
            } else {
                warn!("无法解析代理URL: {}", line);
            }
        }
        
        let mut available_proxies = self.available_proxies.lock().unwrap();
        *available_proxies = proxies;
        
        info!("成功加载 {} 个代理", available_proxies.len());
        Ok(())
    }

    /// 解析代理URL
    fn parse_proxy_url(&self, url: &str) -> Result<ProxyConfig> {
        // 解析 ******************************:port 格式
        let url_parts: Vec<&str> = url.split("://").collect();
        if url_parts.len() != 2 {
            return Err(anyhow::anyhow!("无效的代理URL格式"));
        }
        
        let auth_and_host = url_parts[1];
        let parts: Vec<&str> = auth_and_host.split('@').collect();
        if parts.len() != 2 {
            return Err(anyhow::anyhow!("无效的代理URL格式"));
        }
        
        let auth_parts: Vec<&str> = parts[0].split(':').collect();
        if auth_parts.len() != 2 {
            return Err(anyhow::anyhow!("无效的认证格式"));
        }
        
        let host_port: Vec<&str> = parts[1].split(':').collect();
        if host_port.len() != 2 {
            return Err(anyhow::anyhow!("无效的主机端口格式"));
        }
        
        Ok(ProxyConfig {
            proxy_url: url.to_string(),
            username: auth_parts[0].to_string(),
            password: auth_parts[1].to_string(),
            host: host_port[0].to_string(),
            port: host_port[1].parse()?,
        })
    }

    /// 测试所有代理的连通性（向Jito端点）
    pub async fn test_proxy_connectivity_to_jito(&self) -> Result<()> {
        info!("开始测试代理与Jito端点的连通性...");
        
        let proxies = self.available_proxies.lock().unwrap().clone();
        let jito_endpoints = self.jito_endpoints.lock().unwrap().clone();
        
        if jito_endpoints.is_empty() {
            return Err(anyhow::anyhow!("没有配置Jito端点"));
        }
        
        let test_endpoint = &jito_endpoints[0]; // 使用第一个端点测试
        
        // 创建测试请求
        let test_request = serde_json::json!({
            "jsonrpc": "2.0",
            "id": "proxy_test",
            "method": "getHealth",
            "params": []
        });
        
        let mut working_proxies = Vec::new();
        
        for proxy_config in proxies {
            info!("测试代理: {}:{}", proxy_config.host, proxy_config.port);
            
            // 为代理创建HTTP客户端
            match self.create_proxy_client(&proxy_config).await {
                Ok(client) => {
                    let start_time = Instant::now();
                    match client.post(test_endpoint)
                        .json(&test_request)
                        .send()
                        .await {
                            Ok(_response) => {
                                let duration = start_time.elapsed();
                                info!("✅ 代理 {}:{} 连接成功，响应时间: {}ms", 
                                      proxy_config.host, proxy_config.port, duration.as_millis());
                                working_proxies.push(proxy_config);
                            },
                            Err(e) => {
                                warn!("❌ 代理 {}:{} 连接失败: {}", 
                                      proxy_config.host, proxy_config.port, e);
                            }
                        }
                },
                Err(e) => {
                    warn!("❌ 创建代理客户端失败 {}:{}: {}", 
                          proxy_config.host, proxy_config.port, e);
                }
            }
        }
        
        // 更新可用代理列表
        let mut available_proxies = self.available_proxies.lock().unwrap();
        *available_proxies = working_proxies;
        
        info!("代理连通性测试完成，{} 个代理可用", available_proxies.len());
        Ok(())
    }

    /// 为代理创建HTTP客户端
    async fn create_proxy_client(&self, proxy_config: &ProxyConfig) -> Result<Client> {
        let proxy = Proxy::all(&proxy_config.proxy_url)?
            .basic_auth(&proxy_config.username, &proxy_config.password);
        
        let client = ClientBuilder::new()
            .proxy(proxy)
            .timeout(Duration::from_secs(5))
            .pool_idle_timeout(Duration::from_secs(90))
            .tcp_keepalive(Duration::from_secs(60))
            .pool_max_idle_per_host(10)
            .build()?;
        
        Ok(client)
    }

    /// 预加载所有代理的HTTP客户端
    pub async fn preload_proxy_clients(&self) -> Result<()> {
        info!("开始为所有代理预创建HTTP客户端...");
        
        let proxies = self.available_proxies.lock().unwrap().clone();
        let mut clients = self.client_cache.lock().unwrap();
        
        for proxy_config in proxies {
            match self.create_proxy_client(&proxy_config).await {
                Ok(client) => {
                    clients.insert(proxy_config.proxy_url.clone(), client);
                    info!("为代理 {}:{} 创建了HTTP客户端", 
                          proxy_config.host, proxy_config.port);
                },
                Err(e) => {
                    warn!("为代理 {}:{} 创建HTTP客户端失败: {}", 
                          proxy_config.host, proxy_config.port, e);
                }
            }
        }
        
        info!("代理HTTP客户端预创建完成");
        Ok(())
    }

    /// 轮询获取下一个代理
    pub fn get_next_proxy(&self) -> Option<ProxyConfig> {
        let proxies = self.available_proxies.lock().unwrap();
        if proxies.is_empty() {
            return None;
        }
        
        let mut index = self.current_index.lock().unwrap();
        let proxy = proxies[*index].clone();
        *index = (*index + 1) % proxies.len();
        
        Some(proxy)
    }

    /// 使用代理发送Jito bundle
    pub async fn send_jito_bundle_via_proxy(&self, bundle_data: Value, endpoint: &str) -> Result<Value> {
        let start_total = Instant::now();
        
        // 获取下一个代理
        let proxy_config = match self.get_next_proxy() {
            Some(proxy) => proxy,
            None => return Err(anyhow::anyhow!("没有可用的代理")),
        };
        
        info!("使用代理 {}:{} 发送Jito捆绑包...", proxy_config.host, proxy_config.port);
        
        // 从缓存获取代理客户端
        let client = {
            let clients = self.client_cache.lock().unwrap();
            match clients.get(&proxy_config.proxy_url) {
                Some(client) => client.clone(),
                None => {
                    warn!("代理 {}:{} 的HTTP客户端未在缓存中找到，使用默认客户端", 
                          proxy_config.host, proxy_config.port);
                    self.default_client.clone()
                }
            }
        };
        
        // 发送请求
        let response = client
            .post(endpoint)
            .json(&bundle_data)
            .header("Connection", "keep-alive")
            .send()
            .await?;
        
        let response_data = response.json::<Value>().await?;
        
        // 处理响应
        if let Some(result) = response_data.get("result") {
            info!("使用代理 {}:{} 成功发送捆绑包，bundle id: {}", 
                  proxy_config.host, proxy_config.port, result);
        } else if let Some(error) = response_data.get("error") {
            info!("使用代理 {}:{} 发送失败，错误: {}", 
                  proxy_config.host, proxy_config.port, error);
        }
        
        let total_time = start_total.elapsed();
        info!("通过代理发送Jito捆绑包总耗时: {}ms", total_time.as_millis());
        
        Ok(response_data)
    }

    /// 设置Jito端点列表
    pub fn set_jito_endpoints(&self, endpoints: Vec<String>) {
        let mut jito_endpoints = self.jito_endpoints.lock().unwrap();
        *jito_endpoints = endpoints;
    }
}