mod stats_manager;
use stats_manager::{init_global_stats, record_search};

#[tokio::main]
async fn main() -> Result<()> {
    // ... 现有初始化代码 ...
    
    // 🎯 初始化统计管理器
    let _stats = init_global_stats();
    info!("统计管理器已启动，每秒输出统计信息");
    
    // ... 其余代码保持不变 ...
}
————————————————————————————————————————————
// 在每次 check_arbitrage_opportunity 开始时
record_search();

// 在发现机会时 (real_profit > THRESHOLD_LAMPORTS)
stats_manager::record_opportunity();

// 在日志输出成功信息时自动解析
// 你的现有日志: "使用IP ************* 成功发送捆绑包，bundle id: \"xxx\""
// 会被自动解析并记录
——————————————————————————————————————————————
use anyhow::Result;
use log::info;
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::time;
use std::collections::HashSet;
use std::sync::Mutex;

/// 统计管理器 - 追踪套利机器人的关键指标
pub struct StatsManager {
    /// 总搜索次数 (每次检查套利机会都算一次)
    total_searches: AtomicU64,
    /// 发现的套利机会数量 (真实利润 > 阈值的情况)
    opportunities_found: AtomicU64,
    /// 成功执行套利交易的次数 (收到bundle ID的情况)
    successful_executions: AtomicU64,
    /// 已处理的bundle ID集合，避免重复计数
    processed_bundle_ids: Arc<Mutex<HashSet<String>>>,
    /// 启动时间
    start_time: Instant,
}

impl StatsManager {
    /// 创建新的统计管理器实例
    pub fn new() -> Self {
        Self {
            total_searches: AtomicU64::new(0),
            opportunities_found: AtomicU64::new(0),
            successful_executions: AtomicU64::new(0),
            processed_bundle_ids: Arc::new(Mutex::new(HashSet::new())),
            start_time: Instant::now(),
        }
    }
    
    /// 启动定期统计打印 (每秒一次)
    pub fn start_periodic_stats_printer(self: Arc<Self>) {
        tokio::spawn(async move {
            let mut interval = time::interval(Duration::from_secs(1));
            
            loop {
                interval.tick().await;
                self.print_stats();
            }
        });
    }
    
    /// 记录一次搜索 (每次调用 check_arbitrage_opportunity 时调用)
    pub fn record_search(&self) {
        self.total_searches.fetch_add(1, Ordering::Relaxed);
    }
    
    /// 记录发现套利机会 (真实利润 > 阈值时调用)
    pub fn record_opportunity(&self) {
        self.opportunities_found.fetch_add(1, Ordering::Relaxed);
    }
    
    /// 记录成功执行 (从日志中解析bundle ID时调用)
    pub fn record_success(&self, bundle_id: &str) {
        let mut processed = self.processed_bundle_ids.lock().unwrap();
        
        // 只有新的bundle ID才计数
        if processed.insert(bundle_id.to_string()) {
            self.successful_executions.fetch_add(1, Ordering::Relaxed);
            
            // 限制集合大小，避免内存泄漏
            if processed.len() > 10000 {
                processed.clear();
            }
        }
    }
    
    /// 打印当前统计信息
    pub fn print_stats(&self) {
        let searches = self.total_searches.load(Ordering::Relaxed);
        let opportunities = self.opportunities_found.load(Ordering::Relaxed);
        let successes = self.successful_executions.load(Ordering::Relaxed);
        
        info!(
            "[STATS] Total searches: {}  |  Opportunities: {}  |  Success: {}",
            searches, opportunities, successes
        );
    }
    
    /// 获取统计数据
    pub fn get_stats(&self) -> (u64, u64, u64) {
        (
            self.total_searches.load(Ordering::Relaxed),
            self.opportunities_found.load(Ordering::Relaxed),
            self.successful_executions.load(Ordering::Relaxed),
        )
    }
}

// 实现 Clone trait 以支持 Arc 共享
impl Clone for StatsManager {
    fn clone(&self) -> Self {
        Self {
            total_searches: AtomicU64::new(self.total_searches.load(Ordering::Relaxed)),
            opportunities_found: AtomicU64::new(self.opportunities_found.load(Ordering::Relaxed)),
            successful_executions: AtomicU64::new(self.successful_executions.load(Ordering::Relaxed)),
            processed_bundle_ids: self.processed_bundle_ids.clone(),
            start_time: self.start_time,
        }
    }
}

/// 全局统计管理器实例
static GLOBAL_STATS: std::sync::OnceLock<Arc<StatsManager>> = std::sync::OnceLock::new();

/// 初始化全局统计管理器
pub fn init_global_stats() -> Arc<StatsManager> {
    let stats = Arc::new(StatsManager::new());
    GLOBAL_STATS.set(stats.clone()).expect("统计管理器只能初始化一次");
    
    // 启动定期打印
    stats.clone().start_periodic_stats_printer();
    
    stats
}

/// 获取全局统计管理器
pub fn get_global_stats() -> &'static Arc<StatsManager> {
    GLOBAL_STATS.get().expect("统计管理器未初始化")
}

/// 便捷函数：记录搜索
pub fn record_search() {
    get_global_stats().record_search();
}

/// 便捷函数：记录机会
pub fn record_opportunity() {
    get_global_stats().record_opportunity();
}

/// 便捷函数：记录成功 (自动解析bundle ID)
pub fn record_success_from_log(log_message: &str) {
    // 查找 "bundle id:" 模式
    if let Some(start) = log_message.find("bundle id:") {
        let bundle_part = &log_message[start + 10..]; // "bundle id:" 长度为10
        
        // 提取bundle ID (通常是64字符的十六进制字符串)
        let bundle_id = bundle_part
            .trim()
            .split_whitespace()
            .next()
            .unwrap_or("")
            .trim_matches('"'); // 移除可能的引号
            
        if bundle_id.len() == 64 && bundle_id.chars().all(|c| c.is_ascii_hexdigit()) {
            get_global_stats().record_success(bundle_id);
        }
    }
}

###############################################

版本2stats_manager.rs
// 耗时：1-5纳秒，几乎无感知
pub fn record_search(&self) {
    self.total_searches.fetch_add(1, Ordering::Relaxed);
}

————————————
异步机会记录

// 立即返回，不阻塞套利流程
pub fn record_opportunity(&self) {
    let _ = self.event_sender.send(StatsEvent::Opportunity);
}

————————————————————————————————————————————
在套利检查开始时：
pub async fn check_arbitrage_opportunity(&self, pair_config: &ArbPairConfig) -> Result<()> {
    // 🚀 超快速记录，几乎无耗时
    stats_manager::record_search();
    
    let start = Instant::now();
    // ... 现有逻辑 ...
}

————————————————————————
在发现机会时：
if real_profit > THRESHOLD_LAMPORTS {
    // 🚀 异步记录，立即返回，不影响套利执行
    stats_manager::record_opportunity();
    
    // 执行套利交易
    log::info!("{}: 执行套利交易...", pair_name);
    self.execute_arbitrage(quote0_resp, quote1_resp, jito_tip).await?;
}
——————————————————————————————————————————————————————
在成功发送后：
if let Some(bundle_id) = response.get("result").and_then(|r| r.as_str()) {
    log::info!("发送到Jito端点 {} 成功，bundle id: {}", endpoint, bundle_id);
    
    // 🚀 异步记录，从实际的bundle_id记录，更准确
    stats_manager::record_success_from_log(&format!("bundle id: {}", bundle_id));
}
——————————————————————————————————————————————————
use anyhow::Result;
use log::info;
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::time;
use tokio::sync::mpsc;
use std::collections::HashSet;

/// 统计事件类型
#[derive(Debug, Clone)]
pub enum StatsEvent {
    Search,
    Opportunity,
    Success(String), // bundle_id
}

/// 高性能异步统计管理器
pub struct StatsManager {
    /// 总搜索次数
    total_searches: AtomicU64,
    /// 发现的套利机会数量
    opportunities_found: AtomicU64,
    /// 成功执行套利交易的次数
    successful_executions: AtomicU64,
    /// 启动时间
    start_time: Instant,
    /// 事件发送器
    event_sender: mpsc::UnboundedSender<StatsEvent>,
}

impl StatsManager {
    /// 创建新的统计管理器实例
    pub fn new() -> Self {
        let (event_sender, event_receiver) = mpsc::unbounded_channel();
        
        let stats = Self {
            total_searches: AtomicU64::new(0),
            opportunities_found: AtomicU64::new(0),
            successful_executions: AtomicU64::new(0),
            start_time: Instant::now(),
            event_sender,
        };
        
        // 启动异步事件处理器
        Self::start_event_processor(
            event_receiver,
            stats.opportunities_found.clone(),
            stats.successful_executions.clone(),
        );
        
        stats
    }
    
    /// 启动异步事件处理器
    fn start_event_processor(
        mut event_receiver: mpsc::UnboundedReceiver<StatsEvent>,
        opportunities_counter: Arc<AtomicU64>,
        success_counter: Arc<AtomicU64>,
    ) {
        tokio::spawn(async move {
            let mut processed_bundle_ids = HashSet::new();
            
            while let Some(event) = event_receiver.recv().await {
                match event {
                    StatsEvent::Search => {
                        // Search 已经在主线程中直接处理，这里不需要处理
                    }
                    StatsEvent::Opportunity => {
                        opportunities_counter.fetch_add(1, Ordering::Relaxed);
                    }
                    StatsEvent::Success(bundle_id) => {
                        // 检查是否是新的bundle ID
                        if processed_bundle_ids.insert(bundle_id) {
                            success_counter.fetch_add(1, Ordering::Relaxed);
                        }
                        
                        // 限制集合大小，避免内存泄漏
                        if processed_bundle_ids.len() > 10000 {
                            processed_bundle_ids.clear();
                        }
                    }
                }
            }
        });
    }
    
    /// 启动定期统计打印 (每秒一次)
    pub fn start_periodic_stats_printer(self: Arc<Self>) {
        tokio::spawn(async move {
            let mut interval = time::interval(Duration::from_secs(1));
            
            loop {
                interval.tick().await;
                self.print_stats();
            }
        });
    }
    
    /// 记录一次搜索 (超快速，直接原子操作)
    pub fn record_search(&self) {
        self.total_searches.fetch_add(1, Ordering::Relaxed);
    }
    
    /// 记录发现套利机会 (异步，不阻塞主流程)
    pub fn record_opportunity(&self) {
        // 发送到异步处理器，立即返回
        let _ = self.event_sender.send(StatsEvent::Opportunity);
    }
    
    /// 记录成功执行 (异步，不阻塞主流程)
    pub fn record_success(&self, bundle_id: String) {
        // 发送到异步处理器，立即返回
        let _ = self.event_sender.send(StatsEvent::Success(bundle_id));
    }
    
    /// 从日志消息中解析并记录成功
    pub fn record_success_from_log(&self, log_message: &str) {
        if let Some(bundle_id) = Self::extract_bundle_id(log_message) {
            self.record_success(bundle_id);
        }
    }
    
    /// 提取bundle ID的纯函数
    fn extract_bundle_id(log_message: &str) -> Option<String> {
        // 查找 "bundle id:" 模式
        if let Some(start) = log_message.find("bundle id:") {
            let bundle_part = &log_message[start + 10..];
            
            // 提取bundle ID
            let bundle_id = bundle_part
                .trim()
                .split_whitespace()
                .next()
                .unwrap_or("")
                .trim_matches('"');
                
            // 验证是否为有效的bundle ID (64字符十六进制)
            if bundle_id.len() == 64 && bundle_id.chars().all(|c| c.is_ascii_hexdigit()) {
                return Some(bundle_id.to_string());
            }
        }
        None
    }
    
    /// 打印当前统计信息
    pub fn print_stats(&self) {
        let searches = self.total_searches.load(Ordering::Relaxed);
        let opportunities = self.opportunities_found.load(Ordering::Relaxed);
        let successes = self.successful_executions.load(Ordering::Relaxed);
        
        info!(
            "[STATS] Total searches: {}  |  Opportunities: {}  |  Success: {}",
            searches, opportunities, successes
        );
    }
}

/// 全局统计管理器
static GLOBAL_STATS: std::sync::OnceLock<Arc<StatsManager>> = std::sync::OnceLock::new();

/// 初始化全局统计管理器
pub fn init_global_stats() -> Arc<StatsManager> {
    let stats = Arc::new(StatsManager::new());
    GLOBAL_STATS.set(stats.clone()).expect("统计管理器只能初始化一次");
    
    // 启动定期打印
    stats.clone().start_periodic_stats_printer();
    
    stats
}

/// 获取全局统计管理器
pub fn get_global_stats() -> &'static Arc<StatsManager> {
    GLOBAL_STATS.get().expect("统计管理器未初始化")
}

/// 便捷函数：记录搜索 (超快速)
#[inline]
pub fn record_search() {
    get_global_stats().record_search();
}

/// 便捷函数：记录机会 (异步)
#[inline]
pub fn record_opportunity() {
    get_global_stats().record_opportunity();
}

/// 便捷函数：记录成功 (异步)
#[inline]
pub fn record_success_from_log(log_message: &str) {
    get_global_stats().record_success_from_log(log_message);
}
_____________________________________________________