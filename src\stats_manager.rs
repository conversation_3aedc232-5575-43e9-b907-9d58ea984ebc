use anyhow::Result;
use log::info;
use std::path::Path;
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::time;

/// 统计管理器 - 追踪套利机器人的关键指标
pub struct StatsManager {
    /// 总搜索次数 (每次检查套利机会都算一次)
    total_searches: AtomicU64,
    /// 发现的套利机会数量 (利润 > 0 的情况)
    opportunities_found: AtomicU64,
    /// 成功执行套利交易的次数 (收到bundle ID的情况)
    successful_executions: AtomicU64,
    /// 上次重置统计的时间
    start_time: Instant,
}

impl StatsManager {
    /// 创建新的统计管理器实例
    pub fn new(_data_dir: &Path) -> Result<Self> {
        let stats = Self {
            total_searches: AtomicU64::new(0),
            opportunities_found: AtomicU64::new(0),
            successful_executions: AtomicU64::new(0),
            start_time: Instant::now(),
        };
        
        // 返回统计管理器实例
        Ok(stats)
    }
    
    /// 启动定期统计打印
    pub fn start_periodic_stats_printer(&self) {
        let stats = Arc::new(self.clone());
        tokio::spawn(async move {
            let mut interval = time::interval(Duration::from_secs(1));
            
            loop {
                interval.tick().await;
                stats.print_stats();
            }
        });
    }
    
    /// 增加总搜索次数
    pub fn increment_searches(&self) {
        self.total_searches.fetch_add(1, Ordering::Relaxed);
    }
    
    /// 增加找到的机会数
    pub fn increment_opportunities(&self) {
        self.opportunities_found.fetch_add(1, Ordering::Relaxed);
    }
    
    /// 增加成功执行次数
    pub fn increment_successes(&self) {
        self.successful_executions.fetch_add(1, Ordering::Relaxed);
    }
    
    /// 打印当前统计信息
    pub fn print_stats(&self) {
        let searches = self.total_searches.load(Ordering::Relaxed);
        let opportunities = self.opportunities_found.load(Ordering::Relaxed);
        let successes = self.successful_executions.load(Ordering::Relaxed);
        let uptime = self.start_time.elapsed().as_secs();
        
        info!(
            "Stats: [Uptime: {}s] Total searches: {}  |  Opportunities: {}  |  Success: {}",
            uptime, searches, opportunities, successes
        );
    }
    
    /// 重置所有统计数据
    pub fn reset(&self) {
        self.total_searches.store(0, Ordering::Relaxed);
        self.opportunities_found.store(0, Ordering::Relaxed);
        self.successful_executions.store(0, Ordering::Relaxed);
    }
}

impl Clone for StatsManager {
    fn clone(&self) -> Self {
        Self {
            total_searches: AtomicU64::new(self.total_searches.load(Ordering::Relaxed)),
            opportunities_found: AtomicU64::new(self.opportunities_found.load(Ordering::Relaxed)),
            successful_executions: AtomicU64::new(self.successful_executions.load(Ordering::Relaxed)),
            start_time: self.start_time,
        }
    }
} 