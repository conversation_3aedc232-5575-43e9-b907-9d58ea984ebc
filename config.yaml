# 套利机器人全局配置
poll_interval_ms: 20  # 默认轮询间隔(毫秒)
jito_tip_percentages:  # Jito小费百分比列表(随机选择)
  - 0.4
  - 0.5
  - 0.65
  - 0.85

# 自定义RPC URL(可选)
# rpc_url: "https://solana-rpc.publicnode.com"
# jupiter_api_url: "http://**************:8080"
# jito_rpc_url: "https://frankfurt.mainnet.block-engine.jito.wtf/api/v1/bundles"

# 价格更新间隔(可选，默认30秒)
# price_update_interval_ms: 30000

# 套利代币对列表
arb_pairs:
  # USDC -> 代币1 -> USDC (活跃度高，轮询间隔短)
  - input_mint: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
    output_mint: "9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump"
    amounts: [********, *********, ********, ********]
    only_direct_routes: true
    max_accounts: 20
    name: "USDC-Token1"
    poll_interval_ms: 2  # 每3毫秒检查一次(高频)
  
  # WSOL -> 代币3 -> WSOL (活跃度较低)
  - input_mint: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
    output_mint: "So11111111111111111111111111111111111111112"
    amounts: [********, *********, ********, *********]
    only_direct_routes: false
    max_accounts: 40
    name: "WSOL-Token3"
    poll_interval_ms: 3  # 每15毫秒检查一次(低频) 
